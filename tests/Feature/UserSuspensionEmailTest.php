<?php

namespace Tests\Feature;

use App\Mail\UserSuspended;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class UserSuspensionEmailTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Disable middleware for testing
        $this->withoutMiddleware([
            \App\Http\Middleware\EnsureUserIsAdmin::class,
            \App\Http\Middleware\RequireOtpVerification::class,
            \App\Http\Middleware\AdminRateLimit::class,
        ]);
    }

    /** @test */
    public function suspension_email_is_sent_when_user_is_suspended()
    {
        Mail::fake();

        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.users.suspend', $user), [
                'reason' => 'Test suspension reason',
                'expires_at' => now()->addDays(7)->format('Y-m-d H:i:s'),
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check that any mailable was queued (the EmailService creates a tracking wrapper)
        Mail::assertQueued(\Illuminate\Mail\Mailable::class);
    }

    /** @test */
    public function suspension_email_contains_correct_data()
    {
        Mail::fake();

        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $expiresAt = now()->addDays(7);
        $reason = 'Test suspension reason';

        $this->actingAs($this->admin)
            ->post(route('admin.users.suspend', $user), [
                'reason' => $reason,
                'expires_at' => $expiresAt->format('Y-m-d H:i:s'),
            ]);

        // The EmailService wraps the mailable in a tracking wrapper, so we need to check for any queued mailable
        Mail::assertQueued(function (\Illuminate\Mail\Mailable $mailable) use ($user, $reason, $expiresAt) {
            // Check if this is our tracking wrapper that contains a UserSuspended mailable
            if (method_exists($mailable, 'originalMailable')) {
                $mail = $mailable->originalMailable;
                return $mail instanceof UserSuspended &&
                       $mail->user->id === $user->id &&
                       $mail->suspendedBy->id === $this->admin->id &&
                       $mail->reason === $reason &&
                       $mail->expiresAt->format('Y-m-d H:i') === $expiresAt->format('Y-m-d H:i');
            }
            return $mailable instanceof UserSuspended &&
                   $mailable->user->id === $user->id &&
                   $mailable->suspendedBy->id === $this->admin->id &&
                   $mailable->reason === $reason &&
                   $mailable->expiresAt->format('Y-m-d H:i') === $expiresAt->format('Y-m-d H:i');
        });
    }

    /** @test */
    public function suspension_email_works_with_indefinite_suspension()
    {
        Mail::fake();

        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($this->admin)
            ->post(route('admin.users.suspend', $user), [
                'reason' => 'Indefinite suspension',
            ]);

        // The EmailService wraps the mailable in a tracking wrapper, so we need to check for any queued mailable
        Mail::assertQueued(function (\Illuminate\Mail\Mailable $mailable) use ($user) {
            // Check if this is our tracking wrapper that contains a UserSuspended mailable
            if (method_exists($mailable, 'originalMailable')) {
                $mail = $mailable->originalMailable;
                return $mail instanceof UserSuspended &&
                       $mail->user->id === $user->id &&
                       $mail->expiresAt === null;
            }
            return $mailable instanceof UserSuspended &&
                   $mailable->user->id === $user->id &&
                   $mailable->expiresAt === null;
        });
    }

    /** @test */
    public function user_model_is_refreshed_before_sending_suspension_email()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Ensure suspended_at is initially null
        $this->assertNull($user->suspended_at);

        $this->actingAs($this->admin)
            ->post(route('admin.users.suspend', $user), [
                'reason' => 'Test suspension',
            ]);

        // Refresh the user from database
        $user->refresh();

        // Verify the user was actually suspended
        $this->assertEquals('suspended', $user->status);
        $this->assertNotNull($user->suspended_at);
        $this->assertEquals('Test suspension', $user->suspension_reason);
    }

    /** @test */
    public function suspension_email_template_handles_null_dates_gracefully()
    {
        // Create a user with null suspended_at (simulating the bug scenario)
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
            'suspended_at' => null,
        ]);

        $mail = new UserSuspended($user, $this->admin, 'Test reason', null);

        // This should not throw an exception
        $content = $mail->content();
        
        $this->assertInstanceOf(\Illuminate\Mail\Mailables\Content::class, $content);
        $this->assertEquals('emails.user-suspended', $content->html);
        $this->assertEquals('emails.user-suspended-text', $content->text);
    }

    /** @test */
    public function admin_users_cannot_be_suspended()
    {
        Mail::fake();

        $adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.users.suspend', $adminUser), [
                'reason' => 'Test suspension',
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Admin users cannot be suspended.');

        Mail::assertNotQueued(UserSuspended::class);
    }

    /** @test */
    public function suspension_continues_even_if_email_fails()
    {
        // Force email to fail by using invalid configuration
        config(['mail.mailers.smtp.host' => 'invalid-host']);

        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.users.suspend', $user), [
                'reason' => 'Test suspension',
            ]);

        // Suspension should still succeed
        $response->assertRedirect();
        $response->assertSessionHas('success');

        // User should be suspended despite email failure
        $user->refresh();
        $this->assertEquals('suspended', $user->status);
    }
}
