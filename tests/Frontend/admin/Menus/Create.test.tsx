import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import Create from '@/pages/admin/Menus/Create';

// Mock Inertia
const mockPost = vi.fn();
const mockSetData = vi.fn();

vi.mock('@inertiajs/react', () => ({
    Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    Link: ({ children, href, ...props }: any) => <a href={href} {...props}>{children}</a>,
    useForm: () => ({
        data: {
            name: '',
            location: 'header',
            description: '',
            is_active: true,
        },
        setData: mockSetData,
        post: mockPost,
        processing: false,
        errors: {},
    }),
}));

// Mock toast
vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
}));

// Mock AppLayout
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

// Mock UI components
vi.mock('@/components/ui/card', () => ({
    Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
    CardContent: ({ children, ...props }: any) => <div data-testid="card-content" {...props}>{children}</div>,
    CardDescription: ({ children, ...props }: any) => <div data-testid="card-description" {...props}>{children}</div>,
    CardHeader: ({ children, ...props }: any) => <div data-testid="card-header" {...props}>{children}</div>,
    CardTitle: ({ children, ...props }: any) => <div data-testid="card-title" {...props}>{children}</div>,
}));

vi.mock('@/components/ui/button', () => ({
    Button: ({ children, onClick, type, ...props }: any) => (
        <button onClick={onClick} type={type} data-testid="button" {...props}>
            {children}
        </button>
    ),
}));

vi.mock('@/components/ui/input', () => ({
    Input: ({ onChange, value, ...props }: any) => (
        <input 
            onChange={onChange} 
            value={value} 
            data-testid="input" 
            {...props} 
        />
    ),
}));

vi.mock('@/components/ui/label', () => ({
    Label: ({ children, ...props }: any) => <label data-testid="label" {...props}>{children}</label>,
}));

vi.mock('@/components/ui/textarea', () => ({
    Textarea: ({ onChange, value, ...props }: any) => (
        <textarea 
            onChange={onChange} 
            value={value} 
            data-testid="textarea" 
            {...props} 
        />
    ),
}));

vi.mock('@/components/ui/select', () => ({
    Select: ({ children, onValueChange, ...props }: any) => (
        <div data-testid="select" {...props}>
            {children}
        </div>
    ),
    SelectContent: ({ children, ...props }: any) => <div data-testid="select-content" {...props}>{children}</div>,
    SelectItem: ({ children, value, ...props }: any) => (
        <option value={value} data-testid="select-item" {...props}>
            {children}
        </option>
    ),
    SelectTrigger: ({ children, ...props }: any) => <div data-testid="select-trigger" {...props}>{children}</div>,
    SelectValue: ({ placeholder, ...props }: any) => <span data-testid="select-value" {...props}>{placeholder}</span>,
}));

vi.mock('@/components/ui/switch', () => ({
    Switch: ({ checked, onCheckedChange, ...props }: any) => (
        <input
            type="checkbox"
            checked={checked}
            onChange={(e) => onCheckedChange && onCheckedChange(e.target.checked)}
            data-testid="switch"
            {...props}
        />
    ),
}));

describe('Menu Create Component', () => {
    const mockProps = {
        locations: {
            header: 'Header',
            footer: 'Footer',
            sidebar: 'Sidebar',
        },
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders the create menu form', () => {
        render(<Create {...mockProps} />);
        
        expect(screen.getByText('Create Menu')).toBeInTheDocument();
        expect(screen.getByTestId('app-layout')).toBeInTheDocument();
    });

    it('handles form input changes', () => {
        render(<Create {...mockProps} />);
        
        const nameInput = screen.getByDisplayValue('');
        fireEvent.change(nameInput, { target: { value: 'Test Menu' } });
        
        expect(mockSetData).toHaveBeenCalledWith('name', 'Test Menu');
    });

    it('handles switch toggle', () => {
        render(<Create {...mockProps} />);
        
        const switchElement = screen.getByTestId('switch');
        fireEvent.click(switchElement);
        
        expect(mockSetData).toHaveBeenCalledWith('is_active', expect.any(Boolean));
    });

    it('submits form with correct data', async () => {
        render(<Create {...mockProps} />);
        
        const form = screen.getByRole('form') || screen.getByTestId('card');
        fireEvent.submit(form);
        
        await waitFor(() => {
            expect(mockPost).toHaveBeenCalledWith('/admin/menus', expect.objectContaining({
                onSuccess: expect.any(Function),
                onError: expect.any(Function),
            }));
        });
    });

    it('displays validation errors when present', () => {
        // Mock form with errors
        vi.mocked(vi.importActual('@inertiajs/react')).useForm = () => ({
            data: {
                name: '',
                location: 'header',
                description: '',
                is_active: true,
            },
            setData: mockSetData,
            post: mockPost,
            processing: false,
            errors: {
                name: 'Name is required',
                description: 'Description is required',
            },
        });

        render(<Create {...mockProps} />);
        
        expect(screen.getByText('Name is required')).toBeInTheDocument();
        expect(screen.getByText('Description is required')).toBeInTheDocument();
    });

    it('shows processing state during form submission', () => {
        // Mock form with processing state
        vi.mocked(vi.importActual('@inertiajs/react')).useForm = () => ({
            data: {
                name: 'Test Menu',
                location: 'header',
                description: 'Test Description',
                is_active: true,
            },
            setData: mockSetData,
            post: mockPost,
            processing: true,
            errors: {},
        });

        render(<Create {...mockProps} />);
        
        const submitButton = screen.getByRole('button', { name: /create menu/i });
        expect(submitButton).toBeDisabled();
    });
});
